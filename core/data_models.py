# core/data_models.py
# 目标: 确保我们的ImageData数据模型能够承载所有必要的信息，特别是要能处理不同来源的图像（RAW, TIF等）
from dataclasses import dataclass, field
from typing import Optional
import numpy as np
from datetime import datetime

@dataclass
class ImageData:
    # ImageData中除了包含处理后的Numpy数组外，还应包含原始文件名和时间戳，以便追溯
    """
    The standard data container for a single captured image, passed from
    the Camera Module to the Processing Module.
    """
    # The core image data for analysis, potentially after exponential decay.
    # We use Optional to handle cases where a grab might fail.
    image: Optional[np.ndarray]
    
    # Metadata for traceability and context
    timestamp: Optional[datetime]
    filename: Optional[str]
    source_name: Optional[str] # e.g., "raw_source_a" or "live_camera_brand_x"
    
    # A flag to indicate if the grab was successful
    success: bool = True
    error_message: str = ""

@dataclass
class AnalysisResult:
    """
    The standard data container for the output of the Processing Module.
    It contains everything the Visualization Module needs to display the results.
    """
    # --- Core Status ---
    success: bool
    
    # --- Data for Visualization (only if success is True) ---
    # The original cropped image that was analyzed
    analyzed_image: Optional[np.ndarray] = None
    
    # The final data for all 56 wires, including MP_Value and coordinates
    wire_results: List[Dict[str, Any]] = field(default_factory=list)
    # Example: [{'WireID': 29, 'MP_Value': 123.4, 'X': 1024, 'Y': 1580}, ...]
    
    # Boundary lines for drawing
    y_mid: Optional[int] = None
    final_cutoff_x1: Optional[int] = None
    final_cutoff_x2: Optional[int] = None
    
    # --- Metadata ---
    timestamp: Optional[datetime] = None # Timestamp from the original ImageData
    filename: Optional[str] = None      # Filename from the original ImageData
    processing_time_ms: float = 0.0
    
    # --- Error Info (only if success is False) ---
    error_message: str = ""