# test_b_module.py
# 这是一个用于独立测试 processing_module 的脚本

import yaml
import cv2
import numpy as np
from pathlib import Path

# 从我们的模块中导入需要的类
from core.data_models import ImageData
from processing_module import ImageAnalyzer

def main():
    print("--- [开始独立测试 Processing Module] ---")

    # --- 1. 加载配置 ---
    print("正在加载 config.yaml...")
    with open("config.yaml", "r", encoding="utf-8") as f:
        config = yaml.safe_load(f)
    
    processing_config = config.get('processing', {})
    if not processing_config:
        print("错误: 在config.yaml中未找到'processing'配置块。")
        return

    # --- 2. 模拟A模块：准备一张测试图片 ---
    print("模拟A模块：准备输入数据...")
    # 选择一张我们熟悉的、用于测试的图片
    test_image_path = Path("a/Pic_20250723153119_W5120_H5120_FMono12.raw") # 请确保此文件存在
    if not test_image_path.exists():
        print(f"错误: 测试图片不存在于 {test_image_path}")
        return
        
    # 手动读取并创建ImageData对象
    try:
        raw_data = np.fromfile(test_image_path, dtype=np.uint16)
        image_array = raw_data.reshape((5120, 5120))
        
        # 创建一个和camera_module输出格式完全一样的ImageData对象
        input_data = ImageData(
            image=image_array,
            filename=test_image_path.name,
            # 其他字段使用默认值
        )
        print(f"成功加载测试图片: {input_data.filename}")
    except Exception as e:
        print(f"读取测试图片时出错: {e}")
        return

    # --- 3. 调用B模块的核心功能 ---
    print("\n实例化并调用 ImageAnalyzer...")
    # 初始化分析器
    analyzer = ImageAnalyzer(processing_config)
    
    # 执行分析
    result = analyzer.analyze(input_data)
    
    # 关闭数据库连接
    analyzer.close()
    print("分析完成，数据库连接已关闭。")

    # --- 4. 验证B模块的输出 ---
    print("\n--- [验证分析结果] ---")
    if result.success:
        print("✅ 分析成功！")
        print(f"    - 处理耗时: {result.processing_time_ms:.2f} ms")
        print(f"    - 共找到 {len(result.wire_results)} 条钢丝数据。")
        
        # 打印几条下半部分钢丝的数据进行抽样检查
        bottom_wires = [w for w in result.wire_results if w['WireID'] >= 29]
        if bottom_wires:
            print("\n    下半部分钢丝抽样数据 (前5条):")
            for wire in bottom_wires[:5]:
                print(f"      - WireID: {wire['WireID']}, MP_Value: {wire['MP_Value']:.4f}")
        
        # --- 5. 模拟C模块：进行可视化验证 ---
        print("\n模拟C模块：生成结果标记图进行目视检查...")
        if result.analyzed_image is not None:
            marked_image = cv2.cvtColor(result.analyzed_image, cv2.COLOR_GRAY2BGR)
            
            # 绘制边界线
            cv2.line(marked_image, (0, result.y_mid), (marked_image.shape[1], result.y_mid), (0, 255, 0), 2)
            cv2.line(marked_image, (result.final_cutoff_x1, 0), (result.final_cutoff_x1, result.y_mid), (0, 255, 255), 2)
            cv2.line(marked_image, (result.final_cutoff_x2, result.y_mid), (result.final_cutoff_x2, marked_image.shape[0]), (0, 255, 255), 2)
            
            # 标记谷值点
            for wire in result.wire_results:
                x, y = int(wire['X']), int(wire['Y'])
                cv2.drawMarker(marked_image, (x, y), (0, 0, 255), markerType=cv2.MARKER_TILTED_CROSS, markerSize=15, thickness=2)

            # 显示图像
            cv2.imshow("Validation Result - Press any key to exit", marked_image)
            cv2.waitKey(0)
            cv2.destroyAllWindows()
            print("可视化验证完成。")
        else:
            print("警告: 分析结果中未包含用于可视化的图像。")

    else:
        print(f"❌ 分析失败: {result.error_message}")

if __name__ == "__main__":
    main()