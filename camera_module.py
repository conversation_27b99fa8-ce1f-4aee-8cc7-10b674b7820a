# camera_module.py (V2)

import cv2
import numpy as np
import re
from pathlib import Path
from datetime import datetime
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any

from core.data_models import ImageData

# ===================================================================
# ICameraStrategy Interface (No changes needed)
# ===================================================================

# 目标: 应用“策略模式”，为所有类型的“相机”（无论是模拟的还是真实的）定义一个统一的行为标准（接口）
class ICameraStrategy(ABC):
    @abstractmethod
    def grab_image(self) -> ImageData:
        pass
    @abstractmethod
    def close(self):
        pass

# ===================================================================
# SimulationCameraStrategy (Upgraded for flexible decoding)
# ===================================================================
class SimulationCameraStrategy(ICameraStrategy):
    def __init__(self, config: dict, processing_config: dict):
        print("Initializing SimulationCamera (V2 with flexible decoding)...")
        # Now, we store not just the path, but the whole source config
        self.image_sources = self._load_and_sort_sources(config.get('sources', []))
        self.current_index = 0
        
        self.perform_decay = processing_config.get('perform_exponential_decay', False)
        self.decay_k = processing_config.get('decay_k_factor', 0.7)
        self.previous_decayed_image: Optional[np.ndarray] = None
        
        if not self.image_sources:
            print("WARNING: No image files found in any simulation source.")
        else:
            print(f"Found a total of {len(self.image_sources)} images across all sources.")

    def _get_timestamp_from_filename(self, filepath: Path) -> datetime:
        match = re.search(r'(\d{14})', filepath.name)
        if match:
            return datetime.strptime(match.group(1), '%Y%m%d%H%M%S')
        return datetime.fromtimestamp(filepath.stat().st_mtime)

    def _load_and_sort_sources(self, sources_config: List[dict]) -> List[Dict[str, Any]]:
        """Loads files from sources and sorts them chronologically."""
        all_files_with_config = []
        for source in sources_config:
            folder = Path(source['folder_path'])
            pattern = source['file_pattern']
            if folder.exists():
                for filepath in folder.glob(pattern):
                    # Store the file path along with its specific configuration
                    all_files_with_config.append({
                        "path": filepath,
                        "config": source
                    })
            else:
                print(f"WARNING: Simulation folder not found: {folder}")
        
        all_files_with_config.sort(key=lambda item: self._get_timestamp_from_filename(item['path']))
        return all_files_with_config

    def _read_image_to_numpy(self, filepath: Path, decoding_config: dict) -> Optional[np.ndarray]:
        """
        Reads an image file into a numpy array based on the provided decoding config.
        """
        try:
            bit_depth = decoding_config.get('bit_depth', 8) # Default to 8-bit if not specified
            
            if filepath.suffix.lower() == '.raw':
                # For RAW, we MUST have decoding info
                width = decoding_config.get('width')
                height = decoding_config.get('height')
                if not all([width, height]):
                    raise ValueError("RAW files require 'width' and 'height' in decoding config.")
                
                # Select numpy dtype based on bit_depth
                dtype = np.uint16 if bit_depth > 8 else np.uint8
                
                raw_data = np.fromfile(filepath, dtype=dtype)
                expected_size = width * height
                if raw_data.size != expected_size:
                    print(f"WARNING: File size mismatch for {filepath.name}. "
                          f"Expected {expected_size} pixels, got {raw_data.size}.")
                    return None
                
                return raw_data.reshape((height, width))

            else: # For TIF, BMP, JPG, etc.
                # Use OpenCV's imread, which is generally robust
                # The -1 flag (cv2.IMREAD_UNCHANGED) tries to read the file as is
                image = cv2.imread(str(filepath), cv2.IMREAD_UNCHANGED)
                if image is None:
                    raise IOError("OpenCV could not read the file.")
                
                # If image has more than 2 dimensions (e.g., color), convert to grayscale
                if image.ndim > 2:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                
                # Optional: Verify bit depth if specified
                actual_dtype = image.dtype
                if bit_depth == 16 and actual_dtype != np.uint16:
                    print(f"WARNING: Config expects 16-bit for {filepath.name}, but OpenCV read it as {actual_dtype}.")
                elif bit_depth == 8 and actual_dtype != np.uint8:
                    print(f"WARNING: Config expects 8-bit for {filepath.name}, but OpenCV read it as {actual_dtype}.")

                return image

        except Exception as e:
            print(f"ERROR: Failed to read/decode image file {filepath.name}: {e}")
            return None

    def grab_image(self) -> ImageData:
        if self.current_index >= len(self.image_sources):
            return ImageData(image=None, timestamp=None, filename=None, source_name="simulation", success=False, error_message="End of files")

        item = self.image_sources[self.current_index]
        filepath = item['path']
        source_config = item['config']
        timestamp = self._get_timestamp_from_filename(filepath)
        
        current_image = self._read_image_to_numpy(filepath, source_config.get('decoding', {}))
        
        if current_image is None:
            self.current_index += 1
            return ImageData(image=None, timestamp=timestamp, filename=filepath.name, source_name=source_config['name'], success=False, error_message=f"Failed to read file")

        # --- Perform Exponential Decay ---
        image_for_analysis = current_image
        if self.perform_decay:
            # Promote to float32 for accurate calculation
            current_image_float = current_image.astype(np.float32)
            if self.previous_decayed_image is None:
                self.previous_decayed_image = current_image_float
            else:
                # Ensure dimensions match for addWeighted
                if self.previous_decayed_image.shape != current_image_float.shape:
                    print("WARNING: Image dimension changed. Resetting exponential decay.")
                    self.previous_decayed_image = current_image_float
                else:
                    self.previous_decayed_image = cv2.addWeighted(
                        current_image_float, self.decay_k,
                        self.previous_decayed_image, 1.0 - self.decay_k, 0
                    )
            image_for_analysis = self.previous_decayed_image.astype(current_image.dtype)

        self.current_index += 1
        
        return ImageData(
            image=image_for_analysis,
            timestamp=timestamp,
            filename=filepath.name,
            source_name=source_config['name'],
            success=True
        )

    def close(self):
        print("SimulationCamera closed.")
        pass

# ===================================================================
# CameraController (No changes needed, it's already flexible)
# ===================================================================
class CameraController:
    def __init__(self, config: dict):
        camera_config = config.get('camera', {})
        processing_config = config.get('processing', {})
        mode = camera_config.get('mode', 'simulation')
        
        print(f"CameraController initializing in '{mode}' mode...")
        
        if mode == "simulation":
            self._strategy: ICameraStrategy = SimulationCameraStrategy(camera_config.get('simulation', {}), processing_config)
        else:
            raise ValueError(f"Unknown or unimplemented camera mode: '{mode}'")

    def grab_image(self) -> ImageData:
        return self._strategy.grab_image()

    def close(self):
        self._strategy.close()