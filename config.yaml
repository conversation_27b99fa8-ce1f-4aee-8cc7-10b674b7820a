# config.yaml

# ===================================================================
# Camera Module Configuration
# ===================================================================
camera:
    # Use "simulation" for development/testing from folders.
    # Use "live_brand_x" for production with a real camera.
    mode: "simulation"
    
    simulation:
        # --- Configuration for Simulation Mode ---
        sources:
            - name: "raw_source_a_16bit"
              folder_path: "a"
              file_pattern: "*.raw"
              # For RAW files, decoding info is mandatory
              decoding:
                  bit_depth: 16  # 16-bit
                  width: 5120
                  height: 5120
              
            - name: "raw_source_a_8bit" # Example for 8-bit RAW
              folder_path: "a_8bit"
              file_pattern: "*.raw"
              decoding:
                  bit_depth: 8   # 8-bit
                  width: 1920
                  height: 1080

            - name: "tif_source_b_16bit"
              folder_path: "b_16bit"
              file_pattern: "*.tif"
              # For TIF/BMP etc., decoding is optional but recommended
              decoding:
                  bit_depth: 16 # Helps verify the read image
            
            - name: "tif_source_b_8bit"
              folder_path: "b_8bit"
              file_pattern: "*.tif"
              decoding:
                  bit_depth: 8

    # --- Placeholder for a real camera configuration ---
    live_camera_brand_x:
        camera_id: "SERIAL_NUMBER_12345"
        exposure_time: 20000
        gain: 1.0

# ===================================================================
# Processing Module Configuration
# ===================================================================
processing:
    # Exponential Decay parameters (used by Camera Module)
    perform_exponential_decay: true
    decay_k_factor: 0.7
    
    # ... other processing parameters ...
    crop:
        x_start: 576
        y_start: 800
        height: 2760
    # Core Analysis Algorithm Parameters
    analysis:
        gauss_kernel_size: [3, 3]
        # Parameters for the new morphological cleaning
        open_kernel_size: [1, 5]
        close_kernel_size: [9, 1]
        
        # Cutoff detection
        cutoff_offset: 150
        
        # Valley Finding
        intensity_kernel: [7, 3]
        smoothing_fft_cutoff_ratio: 0.001
        valley_prominence: 5
        valley_distance: 100
        valley_required_width: 5
        
    # Physical Conversion
    scale: 0.4538
    
    # Database Configuration
    database:
        path: "production_data.db"
        table_name: "sensor_data"