import cv2
import numpy as np
import pandas as pd
import sqlite3
import time
from skimage.filters import threshold_isodata
from numpy.fft import fft, ifft
from scipy.signal import find_peaks
from typing import Dict, Any, Optional

# Import our data models
from core.data_models import ImageData, AnalysisResult

# ===================================================================
# Task 3: Database Connection Utility
# ===================================================================
class DatabaseManager:
    """Handles all interactions with the SQLite database."""
    def __init__(self, db_path: str, table_name: str):
        self.db_path = db_path
        self.table_name = table_name
        self.conn = sqlite3.connect(self.db_path)
        self._create_table()

    def _create_table(self):
        """Creates the data table if it doesn't exist."""
        with self.conn:
            self.conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.table_name} (
                    time TEXT NOT NULL,
                    position INTEGER NOT NULL,
                    mp REAL NOT NULL,
                    filename TEXT
                );
            """)
            # Create an index for faster queries
            self.conn.execute(f"CREATE INDEX IF NOT EXISTS idx_time ON {self.table_name} (time);")
            self.conn.execute(f"CREATE INDEX IF NOT EXISTS idx_position ON {self.table_name} (position);")

    def write_results(self, df_results: pd.DataFrame):
        """Writes a DataFrame of results to the database."""
        if df_results.empty:
            return
        
        # Prepare data to match the table schema
        df_to_write = df_results[['Timestamp', 'WireID', 'MP_Value', 'Filename']].copy()
        df_to_write.rename(columns={
            'Timestamp': 'time',
            'WireID': 'position',
            'MP_Value': 'mp',
            'Filename': 'filename'
        }, inplace=True)
        
        # Convert timestamp to string format for SQLite
        df_to_write['time'] = df_to_write['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        with self.conn:
            df_to_write.to_sql(self.table_name, self.conn, if_exists='append', index=False)

    def close(self):
        self.conn.close()

# ===================================================================
# Task 4 & 5: The Main ImageAnalyzer Class
# ===================================================================
class ImageAnalyzer:
    """
    Encapsulates the ultimate performance version of our image analysis pipeline.
    """
    def __init__(self, config: Dict[str, Any]):
        self.params = config
        self.db_manager = DatabaseManager(
            db_path=config['database']['path'],
            table_name=config['database']['table_name']
        )
        print("ImageAnalyzer initialized.")

    def analyze(self, image_data: ImageData) -> AnalysisResult:
        """
        The main public method to analyze an image.
        """
        start_time = time.time()
        
        if not image_data.success or image_data.image is None:
            return AnalysisResult(success=False, error_message="Invalid ImageData received.")

        try:
            # The core analysis logic is now a private method
            analysis_output = self._perform_analysis(image_data.image)
            
            if analysis_output is None:
                raise ValueError("Analysis pipeline returned no output.")

            # Unpack the results
            df_final_results = analysis_output['results_df']
            cropped_img = analysis_output['cropped_image']
            y_mid = analysis_output['y_mid']
            final_cutoff_x1 = analysis_output['cutoff_x1']
            final_cutoff_x2 = analysis_output['cutoff_x2']

            # --- Data Persistence ---
            # Add metadata to the results before writing to DB
            df_final_results['Timestamp'] = image_data.timestamp
            df_final_results['Filename'] = image_data.filename
            self.db_manager.write_results(df_final_results[df_final_results['WireID'] >= 29])
            
            processing_time = (time.time() - start_time) * 1000 # in ms

            # --- Prepare output for visualization ---
            return AnalysisResult(
                success=True,
                analyzed_image=cropped_img,
                wire_results=df_final_results.to_dict('records'),
                y_mid=y_mid,
                final_cutoff_x1=final_cutoff_x1,
                final_cutoff_x2=final_cutoff_x2,
                timestamp=image_data.timestamp,
                filename=image_data.filename,
                processing_time_ms=processing_time
            )

        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            return AnalysisResult(
                success=False,
                error_message=str(e),
                timestamp=image_data.timestamp,
                filename=image_data.filename,
                processing_time_ms=processing_time
            )

    def _perform_analysis(self, image_16bit: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        This is the heart of the module, containing the optimized algorithm
        from our final Notebook version.
        """
        # --- Stage 1: Convert and Crop ---
        image_8bit = (image_16bit / 65535.0 * 255.0).astype(np.uint8) if image_16bit.dtype == np.uint16 else image_16bit
        
        p_crop = self.params['crop']
        cropped_cpu = image_8bit[p_crop['y_start'] : p_crop['y_start'] + p_crop['height'], p_crop['x_start']:]

        # --- Stage 2: GPU-accelerated Preprocessing ---
        gpu_cropped = cv2.UMat(cropped_cpu)
        p_analysis = self.params['analysis']
        gpu_blurred = cv2.GaussianBlur(gpu_cropped, tuple(p_analysis['gauss_kernel_size']), 0)
        clahe = cv2.createCLAHE(clipLimit=0.03, tileGridSize=(64, 64))
        gpu_equalized = clahe.apply(gpu_blurred)
        equalized_cpu = gpu_equalized.get()
        iso_threshold = threshold_isodata(equalized_cpu)
        _, gpu_binary = cv2.threshold(gpu_equalized, iso_threshold, 255, cv2.THRESH_BINARY)
        
        open_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, tuple(p_analysis['open_kernel_size']))
        gpu_opened = cv2.morphologyEx(gpu_binary, cv2.MORPH_OPEN, open_kernel)
        close_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, tuple(p_analysis['close_kernel_size']))
        gpu_pre_repaired = cv2.morphologyEx(gpu_opened, cv2.MORPH_CLOSE, close_kernel)
        pre_repaired_cpu = gpu_pre_repaired.get()

        # --- Stage 3: CPU-based Logic and Analysis ---
        # (This is a direct, clean implementation of our final Notebook logic)
        vertical_projection = np.sum(pre_repaired_cpu, axis=1)
        non_zero_indices = np.where(vertical_projection > 50)[0]
        if len(non_zero_indices) < 2: return None
        search_start_y, search_end_y = non_zero_indices[0], non_zero_indices[-1]
        
        # A more robust way to find zero runs
        diff = np.diff((vertical_projection[search_start_y:search_end_y+1] < 50).astype(int))
        starts = np.where(diff == 1)[0] + 1
        ends = np.where(diff == -1)[0]
        if not np.any(starts) or not np.any(ends): return None
        if ends[0] < starts[0]: ends = ends[1:]
        if starts.size > ends.size: starts = starts[:ends.size]
        if not np.any(starts): return None
        
        run_lengths = ends - starts
        longest_run_idx = np.argmax(run_lengths)
        run_start, run_end = starts[longest_run_idx], ends[longest_run_idx]
        y_mid = search_start_y + run_start + (run_end - run_start) // 2

        contours, _ = cv2.findContours(pre_repaired_cpu, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        contours = sorted(contours, key=cv2.contourArea, reverse=True)
        if len(contours) < 56: return None
        final_contours = sorted(contours[:56], key=lambda c: cv2.boundingRect(c)[1])
        
        top_contours = [c for c in final_contours if cv2.boundingRect(c)[1] + cv2.boundingRect(c)[3]/2 < y_mid]
        bottom_contours = [c for c in final_contours if cv2.boundingRect(c)[1] + cv2.boundingRect(c)[3]/2 >= y_mid]
        def get_cutoff(cnts): return np.min([np.max(c[:,:,0]) for c in cnts]) if cnts else pre_repaired_cpu.shape[1]
        final_cutoff_x1 = max(0, get_cutoff(top_contours) - p_analysis['cutoff_offset'])
        final_cutoff_x2 = max(0, get_cutoff(bottom_contours) - p_analysis['cutoff_offset'])

        all_points = []
        for idx, contour in enumerate(final_contours):
            cy = cv2.boundingRect(contour)[1] + cv2.boundingRect(contour)[3]/2
            cutoff_x = final_cutoff_x1 if cy < y_mid else final_cutoff_x2
            y_coords, x_coords = np.nonzero(cv2.drawContours(np.zeros_like(pre_repaired_cpu), [contour], -1, 255, -1))
            valid_mask = x_coords < cutoff_x
            df_points = pd.DataFrame({'x': x_coords[valid_mask], 'y': y_coords[valid_mask]})
            if df_points.empty: continue
            centerline_df = df_points.groupby('x')['y'].mean().round().astype(int).reset_index()
            centerline_df['ObjectIndex'] = idx
            all_points.append(centerline_df)

        if not all_points: return None
        df_all_centerlines = pd.concat(all_points, ignore_index=True)
        
        mean_filtered_img = cv2.boxFilter(cropped_cpu, -1, tuple(p_analysis['intensity_kernel']), normalize=True)
        y_coords_all = df_all_centerlines['y'].to_numpy()
        x_coords_all = df_all_centerlines['x'].to_numpy()
        df_all_centerlines['Mean'] = mean_filtered_img[y_coords_all, x_coords_all]

        all_valleys = []
        for obj_index, group in df_all_centerlines.groupby('ObjectIndex'):
            if len(group) < 30: continue
            mean_intensity = group['Mean'].to_numpy()
            smoothed = np.real(ifft(fft(mean_intensity) * (np.arange(len(mean_intensity)) < len(mean_intensity)*p_analysis['smoothing_fft_cutoff_ratio'])))
            peaks, props = find_peaks(-smoothed, prominence=p_analysis['valley_prominence'], distance=p_analysis['valley_distance'], width=p_analysis['valley_required_width'])
            best_idx = np.argmin(smoothed) if len(peaks) == 0 else peaks[np.argmax(props['prominences'])]
            all_valleys.append({'ObjectIndex': obj_index, 'X': group.iloc[best_idx]['x'], 'Y': group.iloc[best_idx]['y'], 'SmoothedValue': smoothed[best_idx]})
        
        if not all_valleys: return None
        
        df_results = pd.DataFrame(all_valleys)
        df_results['WireID'] = df_results['ObjectIndex'] + 1
        df_results['MP_Value'] = np.where(df_results['ObjectIndex'] < 28, (final_cutoff_x1 - df_results['X'])*self.params['scale'], (final_cutoff_x2 - df_results['X'])*self.params['scale'])
        
        # Return all necessary data in a dictionary
        return {
            "results_df": df_results,
            "cropped_image": cropped_cpu,
            "y_mid": y_mid,
            "cutoff_x1": final_cutoff_x1,
            "cutoff_x2": final_cutoff_x2
        }

    def close(self):
        """Closes the database connection."""
        self.db_manager.close()
        print("ImageAnalyzer closed.")