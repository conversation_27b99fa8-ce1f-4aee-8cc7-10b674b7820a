# =================================================
# Dependencies for the Industrial Vision System MVP
# =================================================

# --- Core Scientific & Image Processing Libraries ---
numpy
opencv-python
pandas
scikit-image
scipy

# --- Configuration ---
pyyaml

# --- Visualization & UI ---
matplotlib      # For basic plotting, real-time display, and debugging
streamlit       # The main web application framework
plotly          # The core engine for creating interactive charts in Streamlit

# --- Utilities ---
tqdm            # For displaying progress bars during batch processing